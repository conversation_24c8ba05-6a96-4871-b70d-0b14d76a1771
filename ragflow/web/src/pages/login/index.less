@import '../../theme/vars';

.loginPage {
  display: flex;
  background-color: rgba(255, 255, 255, 0.1);

  .loginLeft {
    // width: 610px;
    width: 40%;
    background-color: rgba(255, 255, 255, 0.1);
    height: 100vh;
    display: flex;
    align-items: center;
  }

  .leftContainer {
    width: 60%;
    padding: 5px, 0px, 5px, 0px;
    margin: 0 auto;
  }

  .thirdPartyLoginButton {
    margin-top: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    // padding-top: 0px;

    .ant-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      font-size: 14px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      background: #fff;
      color: rgba(0, 0, 0, 0.85);
      transition: all 0.3s;

      &:hover {
        color: #40a9ff;
        border-color: #40a9ff;
      }

      .anticon {
        font-size: 16px;
        margin-right: 8px;
      }
    }
  }

  .loginRight {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    position: relative;
    &::before {
      content: ' ';
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: rgba(24, 73, 169, 0.6);
      background-image: url('@/assets/svg/login-background.svg');
      background-size: cover;
      background-blend-mode: multiply;
      filter: blur(3px);
      background-position: center;
      z-index: -1;
    }
    .white {
    }
    .pink {
      color: #e9d7fe;
    }
    .rightPanel {
      max-width: 670px;
      .loginTitle {
        font-size: 68px;
        font-style: normal;
        font-weight: 600;
        line-height: 90px;
        letter-spacing: -1.44px;
      }
      .loginDescription {
        font-size: 20px;
        font-style: normal;
        font-weight: 500;
        line-height: 30px;
      }
      .loginRateNumber {
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
      }
      .loginRateReviews {
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
      }
    }
  }

  .loginTitle {
    //styleName: Heading/1;
    font-size: 38px;
    font-weight: 600;
    line-height: 46px;
    letter-spacing: 0em;
    height: 80px;
    margin-bottom: 69px;

    // text-align: center;
    span {
      font-size: 16px;
      line-height: 24px;
    }
  }

  @media screen and (max-width: 957px) {
    .loginLeft {
      width: 100%;
      height: 100%;
    }

    .modal {
      width: 80%;
    }
  }
}
