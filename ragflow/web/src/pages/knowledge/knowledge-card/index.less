.container {
  height: 251px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .delete {
    height: 24px;
  }

  .content {
    display: flex;
    justify-content: space-between;

    .context {
      flex: 1;
    }
  }

  .footer {
    // text-align: left;
  }
  .footerTop {
    padding-bottom: 2px;
  }

  .titleWrapper {
    margin: 16px 0;
    overflow: hidden;

    .title {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      font-size: 24px;
      line-height: 32px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.88);
      word-break: break-all;
    }

    .description {
      margin-top: 4px;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      font-size: 12px;
      font-weight: 600;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.45);
    }
    .titledark {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      font-size: 24px;
      line-height: 32px;
      font-weight: 600;
      word-break: break-all;
    }

    .descriptiondark {
      margin-top: 4px;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      font-size: 12px;
      font-weight: 600;
      line-height: 20px;
    }
  }
}

.card {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  padding: 24px;
  width: 300px;
  cursor: pointer;

  .titleWrapper {
    .title {
      font-size: 24px;
      line-height: 32px;
      font-weight: 600;
      word-break: break-all;
    }
    .description {
      font-size: 12px;
      font-weight: 600;
      line-height: 20px;
    }
  }

  :global {
    .ant-card-body {
      padding: 0;
      margin: 0;
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .bottomLeft {
    vertical-align: middle;
  }
  .leftIcon {
    margin-right: 10px;
    font-size: 18px;
    vertical-align: middle;
  }
  .rightText {
    font-size: 12px;
    font-weight: 600;
    vertical-align: middle;
  }
}

.hideRibbon {
  display: none !important;
}

.ribbon {
  top: 4px;
}
