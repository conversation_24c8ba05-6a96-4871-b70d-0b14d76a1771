.editableRow {
  :global(.editable-cell) {
    position: relative;
  }

  :global(.editable-cell-value-wrap) {
    padding: 5px 12px;
    cursor: pointer;
    height: 30px !important;
  }
  &:hover {
    :global(.editable-cell-value-wrap) {
      padding: 4px 11px;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
    }
  }
}

.dynamicParameterVariable {
  background-color: #ebe9e950;
  :global(.ant-collapse-content) {
    background-color: #f6f6f634;
  }
  :global(.ant-collapse-content-box) {
    padding: 0 !important;
  }
  margin-bottom: 20px;
  .title {
    font-weight: 600;
    font-size: 16px;
  }
  .variableType {
    width: 30%;
  }
  .variableValue {
    flex: 1;
  }

  .addButton {
    color: rgb(22, 119, 255);
    font-weight: 600;
  }
}
